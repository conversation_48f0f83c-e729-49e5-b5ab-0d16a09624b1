[2025-09-01 17:11:15] local.ERROR: Route [api.branches.by-city] not defined. {"view":{"view":"D:\\laragon\\www\\adawliahshop\\platform\\plugins\\branch-management\\resources\\views\\product\\branches.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1424308544 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#4749</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1424308544\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","product":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Botble\\Ecommerce\\Models\\Product</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24903 title=\"2 occurrences\">#4903</a><samp data-depth=1 id=sf-dump-*********-ref24903 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">ec_products</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:46</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Xbox One Wireless Controller Black Color</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"\"\"
      <span class=sf-dump-str title=\"328 characters\">&lt;ul&gt;&lt;li&gt; Unrestrained and portable active stereo speaker&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"328 characters\">            &lt;li&gt; Free from the confines of wires and chords&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"328 characters\">            &lt;li&gt; 20 hours of portable capabilities&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"328 characters\">            &lt;li&gt; Double-ended Coil Cord with 3.5mm Stereo Plugs Included&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"328 characters\">            &lt;li&gt; 3/4&#8243; Dome Tweeters: 2X and 4&#8243; Woofer: 1X&lt;/li&gt;&lt;/ul&gt;</span>
      \"\"\"
    \"<span class=sf-dump-key>content</span>\" => \"\"\"
      <span class=sf-dump-str title=\"1910 characters\">&lt;p&gt;Short Hooded Coat features a straight body, large pockets with button flaps, ventilation air holes, and a string detail along the hemline. The style is completed with a drawstring hood, featuring Rains&#8217; signature built-in cap. Made from waterproof, matte PU, this lightweight unisex rain jacket is an ode to nostalgia through its classic silhouette and utilitarian design details.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Casual unisex fit&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- 64% polyester, 36% polyurethane&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Water column pressure: 4000 mm&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Model is 187cm tall and wearing a size S / M&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Unisex fit&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Drawstring hood with built-in cap&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Front placket with snap buttons&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Ventilation under armpit&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Adjustable cuffs&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Double welted front pockets&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Adjustable elastic string at hempen&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Ultrasonically welded seams&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;This is a unisex item, please check our clothing &amp;amp; footwear sizing guide for specific Rains jacket sizing information. RAINS comes from the rainy nation of Denmark at the edge of the European continent, close to the ocean and with prevailing westerly winds; all factors that contribute to an average of 121 rain days each year. Arising from these rainy weather conditions comes the attitude that a quick rain shower may be beautiful, as well as moody- but first and foremost requires the right outfit. Rains focus on the whole experience of going outside on rainy days, issuing an invitation to explore even in the most mercurial weather.&lt;/p&gt;</span>
      \"\"\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"83 characters\">[&quot;products\\/11.jpg&quot;,&quot;products\\/11-1.jpg&quot;,&quot;products\\/11-2.jpg&quot;,&quot;products\\/11-3.jpg&quot;]</span>\"
    \"<span class=sf-dump-key>video_media</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SW-140-A0</span>\"
    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>12</span>
    \"<span class=sf-dump-key>allow_checkout_when_out_of_stock</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>with_storehouse_management</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>7</span>
    \"<span class=sf-dump-key>is_variation</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>sale_type</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>1130.0</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>start_date</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>end_date</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>length</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>wide</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>height</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>tax_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>views</span>\" => <span class=sf-dump-num>175032</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-13 22:15:52</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-13 22:16:01</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Enums\\StockStatusEnum
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">StockStatusEnum</span></span> {<a class=sf-dump-ref>#5467</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"8 characters\">in_stock</span>\"
    </samp>}
    \"<span class=sf-dump-key>created_by_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>created_by_type</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Botble\\ACL\\Models\\User</span>\"
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"15 characters\">products/11.jpg</span>\"
    \"<span class=sf-dump-key>product_type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">physical</span>\"
    \"<span class=sf-dump-key>barcode</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>cost_per_item</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>generate_license_code</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>license_code_type</span>\" => \"<span class=sf-dump-str title=\"13 characters\">auto_generate</span>\"
    \"<span class=sf-dump-key>minimum_order_quantity</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>maximum_order_quantity</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>notify_attachment_updated</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>specification_table_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>approved_by</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>9</span>
    \"<span class=sf-dump-key>reviews_avg</span>\" => <span class=sf-dump-num>2.777778</span>
    \"<span class=sf-dump-key>defaultVariation</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Models\\ProductVariation
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductVariation</span></span> {<a class=sf-dump-ref>#5481</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"21 characters\">ec_product_variations</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:45</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Xbox One Wireless Controller Black Color</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"\"\"
      <span class=sf-dump-str title=\"328 characters\">&lt;ul&gt;&lt;li&gt; Unrestrained and portable active stereo speaker&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"328 characters\">            &lt;li&gt; Free from the confines of wires and chords&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"328 characters\">            &lt;li&gt; 20 hours of portable capabilities&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"328 characters\">            &lt;li&gt; Double-ended Coil Cord with 3.5mm Stereo Plugs Included&lt;/li&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"328 characters\">            &lt;li&gt; 3/4&#8243; Dome Tweeters: 2X and 4&#8243; Woofer: 1X&lt;/li&gt;&lt;/ul&gt;</span>
      \"\"\"
    \"<span class=sf-dump-key>content</span>\" => \"\"\"
      <span class=sf-dump-str title=\"1910 characters\">&lt;p&gt;Short Hooded Coat features a straight body, large pockets with button flaps, ventilation air holes, and a string detail along the hemline. The style is completed with a drawstring hood, featuring Rains&#8217; signature built-in cap. Made from waterproof, matte PU, this lightweight unisex rain jacket is an ode to nostalgia through its classic silhouette and utilitarian design details.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Casual unisex fit&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- 64% polyester, 36% polyurethane&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Water column pressure: 4000 mm&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Model is 187cm tall and wearing a size S / M&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Unisex fit&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Drawstring hood with built-in cap&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Front placket with snap buttons&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Ventilation under armpit&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Adjustable cuffs&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Double welted front pockets&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Adjustable elastic string at hempen&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;- Ultrasonically welded seams&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>
      <span class=sf-dump-str title=\"1910 characters\">                                &lt;p&gt;This is a unisex item, please check our clothing &amp;amp; footwear sizing guide for specific Rains jacket sizing information. RAINS comes from the rainy nation of Denmark at the edge of the European continent, close to the ocean and with prevailing westerly winds; all factors that contribute to an average of 121 rain days each year. Arising from these rainy weather conditions comes the attitude that a quick rain shower may be beautiful, as well as moody- but first and foremost requires the right outfit. Rains focus on the whole experience of going outside on rainy days, issuing an invitation to explore even in the most mercurial weather.&lt;/p&gt;</span>
      \"\"\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"83 characters\">[&quot;products\\/11.jpg&quot;,&quot;products\\/11-1.jpg&quot;,&quot;products\\/11-2.jpg&quot;,&quot;products\\/11-3.jpg&quot;]</span>\"
    \"<span class=sf-dump-key>video_media</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SW-140-A0</span>\"
    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>12</span>
    \"<span class=sf-dump-key>allow_checkout_when_out_of_stock</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>with_storehouse_management</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>7</span>
    \"<span class=sf-dump-key>is_variation</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>sale_type</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>1130.0</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>start_date</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>end_date</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>length</span>\" => <span class=sf-dump-num>13.0</span>
    \"<span class=sf-dump-key>wide</span>\" => <span class=sf-dump-num>16.0</span>
    \"<span class=sf-dump-key>height</span>\" => <span class=sf-dump-num>17.0</span>
    \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-num>786.0</span>
    \"<span class=sf-dump-key>tax_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>views</span>\" => <span class=sf-dump-num>175032</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-13 22:15:52</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-13 22:16:01</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">in_stock</span>\"
    \"<span class=sf-dump-key>created_by_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>created_by_type</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Botble\\ACL\\Models\\User</span>\"
    \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>product_type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">physical</span>\"
    \"<span class=sf-dump-key>barcode</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>cost_per_item</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>generate_license_code</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>license_code_type</span>\" => \"<span class=sf-dump-str title=\"13 characters\">auto_generate</span>\"
    \"<span class=sf-dump-key>minimum_order_quantity</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>maximum_order_quantity</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>notify_attachment_updated</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>specification_table_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>approved_by</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>9</span>
    \"<span class=sf-dump-key>reviews_avg</span>\" => <span class=sf-dump-num>2.777778</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:28</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Botble\\Base\\Enums\\BaseStatusEnum</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Botble\\Ecommerce\\Enums\\StockStatusEnum</span>\"
    \"<span class=sf-dump-key>product_type</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Botble\\Ecommerce\\Enums\\ProductTypeEnum</span>\"
    \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>sale_type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>minimum_order_quantity</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>maximum_order_quantity</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"4 characters\">bool</span>\"
    \"<span class=sf-dump-key>allow_checkout_when_out_of_stock</span>\" => \"<span class=sf-dump-str title=\"4 characters\">bool</span>\"
    \"<span class=sf-dump-key>with_storehouse_management</span>\" => \"<span class=sf-dump-str title=\"4 characters\">bool</span>\"
    \"<span class=sf-dump-key>generate_license_code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">bool</span>\"
    \"<span class=sf-dump-key>notify_attachment_updated</span>\" => \"<span class=sf-dump-str title=\"4 characters\">bool</span>\"
    \"<span class=sf-dump-key>video_media</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"
    \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>wide</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>height</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>views</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>cost_per_item</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>is_variation</span>\" => \"<span class=sf-dump-str title=\"4 characters\">bool</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">original_price</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">front_sale_price</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>slugable</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Slug\\Models\\Slug
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Slug\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Slug</span></span> {<a class=sf-dump-ref>#4925</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">slugs</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>defaultVariation</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Models\\ProductVariation
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductVariation</span></span> {<a class=sf-dump-ref>#4934</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"21 characters\">ec_product_variations</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>productCollections</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4950</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>productLabels</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4937</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Marketplace\\Models\\Store
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Marketplace\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref>#4978</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">mp_stores</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:23</span> [ &#8230;23]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:23</span> [ &#8230;23]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:19</span> [ &#8230;19]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>tags</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4985</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>categories</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#5026</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>options</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#5047</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>crossSales</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#5063</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>metadata</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#5325</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>variations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#5430</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>brand</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Models\\Brand
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Brand</span></span> {<a class=sf-dump-ref>#5436</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">ec_brands</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:34</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">content</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"11 characters\">video_media</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"32 characters\">allow_checkout_when_out_of_stock</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"26 characters\">with_storehouse_management</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"12 characters\">is_variation</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"9 characters\">sale_type</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"10 characters\">start_date</span>\"
    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">end_date</span>\"
    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"6 characters\">length</span>\"
    <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"4 characters\">wide</span>\"
    <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"6 characters\">height</span>\"
    <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"6 characters\">weight</span>\"
    <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"6 characters\">tax_id</span>\"
    <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"5 characters\">views</span>\"
    <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"12 characters\">stock_status</span>\"
    <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"7 characters\">barcode</span>\"
    <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"13 characters\">cost_per_item</span>\"
    <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"21 characters\">generate_license_code</span>\"
    <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"17 characters\">license_code_type</span>\"
    <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"22 characters\">minimum_order_quantity</span>\"
    <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"22 characters\">maximum_order_quantity</span>\"
    <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"25 characters\">notify_attachment_updated</span>\"
    <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"22 characters\">specification_table_id</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">originalPrice</span>: <span class=sf-dump-num>1130.0</span>
  #<span class=sf-dump-protected title=\"Protected property\">finalPrice</span>: <span class=sf-dump-num>1130.0</span>
  #<span class=sf-dump-protected title=\"Protected property\">priceObject</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\ValueObjects\\ProductPrice
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\ValueObjects</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ProductPrice</span></span> {<a class=sf-dump-ref>#4846</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">state</span>: <span class=sf-dump-const title=\"Uninitialized property\">? mixed</span>
    #<span class=sf-dump-protected title=\"Protected property\">variations</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Illuminate\\Database\\Eloquent\\Collection</span>
    #<span class=sf-dump-protected title=\"Protected property\">minimumVariation</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Botble\\Ecommerce\\Models\\Product</span>
    #<span class=sf-dump-protected title=\"Protected property\">maximumVariation</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Botble\\Ecommerce\\Models\\Product</span>
    #<span class=sf-dump-protected title=\"Protected property\">product</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Models\\Product
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24903 title=\"2 occurrences\">#4903</a>}
  </samp>}
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [api.branches.by-city] not defined. at D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(885): Illuminate\\Routing\\UrlGenerator->route('api.branches.by...', Array, true)
#1 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\branch-management\\resources\\views\\product\\branches.blade.php(9): route('api.branches.by...')
#2 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Botble\\Shortcode\\View\\View->renderContents()
#9 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\branch-management\\src\\Providers\\HookServiceProvider.php(69): Illuminate\\View\\View->render()
#10 [internal function]: Botble\\BranchManagement\\Providers\\HookServiceProvider->addBranchesToProduct('<button type=\"b...', Object(Botble\\Ecommerce\\Models\\Product))
#11 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Array, Array)
#12 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Base\\Supports\\Filter->fire('product_detail_...', Array)
#13 D:\\laragon\\www\\adawliahshop\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#14 D:\\laragon\\www\\adawliahshop\\platform\\themes\\martfury\\views\\ecommerce\\product.blade.php(126): apply_filters('product_detail_...', NULL, Object(Botble\\Ecommerce\\Models\\Product))
#15 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#16 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#17 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#18 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#19 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#20 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#21 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Botble\\Shortcode\\View\\View->renderContents()
#22 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\theme\\src\\Theme.php(683): Illuminate\\View\\View->render()
#23 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\theme\\src\\Theme.php(662): Botble\\Theme\\Theme->setUpContent('theme.martfury:...', Array)
#24 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->scope('ecommerce.produ...', Array, 'plugins/ecommer...')
#25 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\theme\\src\\Http\\Controllers\\PublicController.php(86): Illuminate\\Support\\Facades\\Facade::__callStatic('scope', Array)
#26 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\theme\\src\\Http\\Controllers\\PublicController.php(116): Botble\\Theme\\Http\\Controllers\\PublicController->getView('xbox-one-wirele...', 'products')
#27 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Theme\\Http\\Controllers\\PublicController->getViewWithPrefix('products', 'xbox-one-wirele...')
#28 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getViewWithPref...', Array)
#29 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Theme\\Http\\Controllers\\PublicController), 'getViewWithPref...')
#30 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#31 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#32 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(81): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\EnsureLicenseHasBeenActivated.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\currency-symbol-updater\\src\\Http\\Middleware\\ReplaceSarCurrencySymbol.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\CurrencySymbolUpdater\\Http\\Middleware\\ReplaceSarCurrencySymbol->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\TrackAbandonedCart.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\TrackAbandonedCart->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureCouponMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\adawliahshop\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#70 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#77 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#79 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#80 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\laragon\\www\\adawliahshop\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#93 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#94 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#95 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#96 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#97 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#98 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#99 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#100 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#101 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#102 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#103 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#104 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#105 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#106 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#107 D:\\laragon\\www\\adawliahshop\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#108 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [api.branches.by-city] not defined. at D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(885): Illuminate\\Routing\\UrlGenerator->route('api.branches.by...', Array, true)
#1 D:\\laragon\\www\\adawliahshop\\storage\\framework\\views\\7530feaa1b7821834919ae9a35437f94.php(8): route('api.branches.by...')
#2 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Botble\\Shortcode\\View\\View->renderContents()
#9 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\branch-management\\src\\Providers\\HookServiceProvider.php(69): Illuminate\\View\\View->render()
#10 [internal function]: Botble\\BranchManagement\\Providers\\HookServiceProvider->addBranchesToProduct('<button type=\"b...', Object(Botble\\Ecommerce\\Models\\Product))
#11 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Array, Array)
#12 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Base\\Supports\\Filter->fire('product_detail_...', Array)
#13 D:\\laragon\\www\\adawliahshop\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#14 D:\\laragon\\www\\adawliahshop\\storage\\framework\\views\\edf3d5c22e7dececa1d527cd9dc39d78.php(132): apply_filters('product_detail_...', NULL, Object(Botble\\Ecommerce\\Models\\Product))
#15 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#16 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#17 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#18 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#19 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#20 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#21 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Botble\\Shortcode\\View\\View->renderContents()
#22 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\theme\\src\\Theme.php(683): Illuminate\\View\\View->render()
#23 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\theme\\src\\Theme.php(662): Botble\\Theme\\Theme->setUpContent('theme.martfury:...', Array)
#24 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->scope('ecommerce.produ...', Array, 'plugins/ecommer...')
#25 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\theme\\src\\Http\\Controllers\\PublicController.php(86): Illuminate\\Support\\Facades\\Facade::__callStatic('scope', Array)
#26 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\theme\\src\\Http\\Controllers\\PublicController.php(116): Botble\\Theme\\Http\\Controllers\\PublicController->getView('xbox-one-wirele...', 'products')
#27 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Theme\\Http\\Controllers\\PublicController->getViewWithPrefix('products', 'xbox-one-wirele...')
#28 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getViewWithPref...', Array)
#29 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Theme\\Http\\Controllers\\PublicController), 'getViewWithPref...')
#30 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#31 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#32 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(81): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\EnsureLicenseHasBeenActivated.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\currency-symbol-updater\\src\\Http\\Middleware\\ReplaceSarCurrencySymbol.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\CurrencySymbolUpdater\\Http\\Middleware\\ReplaceSarCurrencySymbol->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\TrackAbandonedCart.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\TrackAbandonedCart->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureCouponMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\adawliahshop\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#70 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#77 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#79 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#80 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\laragon\\www\\adawliahshop\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#93 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#94 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#95 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#96 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#97 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#98 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#99 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#100 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#101 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#102 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#103 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#104 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#105 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#106 D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#107 D:\\laragon\\www\\adawliahshop\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#108 {main}
"} 
[2025-09-01 17:13:24] local.ERROR: Symfony\Component\Routing\Exception\RouteNotFoundException: Route [ecommerce.branch-inventory.edit] not defined. in D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\UrlGenerator.php:526
Stack trace:
#0 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\helpers.php(885): Illuminate\Routing\UrlGenerator->route('ecommerce.branc...', Array, true)
#1 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Supports\Builders\HasUrl.php(53): route('ecommerce.branc...', Array, true)
#2 [internal function]: Botble\Table\Actions\Action->Botble\Base\Supports\Builders\{closure}(Object(Botble\Table\Actions\EditAction))
#3 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Supports\Builders\HasUrl.php(35): call_user_func(Object(Closure), Object(Botble\Table\Actions\EditAction))
#4 D:\laragon\www\adawliahshop\storage\framework\views\4c0570ea60614c946659ef4ee6ca829d.php(2): Botble\Table\Actions\Action->getUrl()
#5 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Filesystem\Filesystem.php(123): require('D:\\laragon\\www\\...')
#6 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Filesystem\Filesystem.php(124): Illuminate\Filesystem\Filesystem::Illuminate\Filesystem\{closure}()
#7 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(57): Illuminate\Filesystem\Filesystem->getRequire('D:\\laragon\\www\\...', Array)
#8 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(76): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\laragon\\www\\...', Array)
#9 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(208): Illuminate\View\Engines\CompilerEngine->get('D:\\laragon\\www\\...', Array)
#10 D:\laragon\www\adawliahshop\vendor\botble\shortcode\src\View\View.php(50): Illuminate\View\View->getContents()
#11 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(160): Botble\Shortcode\View\View->renderContents()
#12 D:\laragon\www\adawliahshop\storage\framework\views\db0fbf0420c2e35ad9892595e4fd2723.php(7): Illuminate\View\View->render()
#13 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Filesystem\Filesystem.php(123): require('D:\\laragon\\www\\...')
#14 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Filesystem\Filesystem.php(124): Illuminate\Filesystem\Filesystem::Illuminate\Filesystem\{closure}()
#15 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(57): Illuminate\Filesystem\Filesystem->getRequire('D:\\laragon\\www\\...', Array)
#16 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(76): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\laragon\\www\\...', Array)
#17 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(208): Illuminate\View\Engines\CompilerEngine->get('D:\\laragon\\www\\...', Array)
#18 D:\laragon\www\adawliahshop\vendor\botble\shortcode\src\View\View.php(50): Illuminate\View\View->getContents()
#19 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(160): Botble\Shortcode\View\View->renderContents()
#20 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableActionAbstract.php(112): Illuminate\View\View->render()
#21 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Collections\helpers.php(236): Botble\Table\Abstracts\TableActionAbstract->Botble\Table\Abstracts\{closure}()
#22 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Supports\Renderable.php(54): value(Object(Closure))
#23 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableActionAbstract.php(108): Botble\Table\Abstracts\TableActionAbstract->rendering(Object(Closure))
#24 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableActionAbstract.php(118): Botble\Table\Abstracts\TableActionAbstract->render()
#25 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Support\helpers.php(134): Botble\Table\Abstracts\TableActionAbstract->toHtml()
#26 D:\laragon\www\adawliahshop\storage\framework\views\4c96db657277edc9903923529034c412.php(10): e(Object(Botble\Table\Actions\EditAction))
#27 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Filesystem\Filesystem.php(123): require('D:\\laragon\\www\\...')
#28 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Filesystem\Filesystem.php(124): Illuminate\Filesystem\Filesystem::Illuminate\Filesystem\{closure}()
#29 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(57): Illuminate\Filesystem\Filesystem->getRequire('D:\\laragon\\www\\...', Array)
#30 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(76): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\laragon\\www\\...', Array)
#31 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(208): Illuminate\View\Engines\CompilerEngine->get('D:\\laragon\\www\\...', Array)
#32 D:\laragon\www\adawliahshop\vendor\botble\shortcode\src\View\View.php(50): Illuminate\View\View->getContents()
#33 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(160): Botble\Shortcode\View\View->renderContents()
#34 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Columns\RowActionsColumn.php(45): Illuminate\View\View->render()
#35 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Columns\FormattedColumn.php(44): Botble\Table\Columns\RowActionsColumn->formattedValue('')
#36 [internal function]: Botble\Table\Columns\FormattedColumn::Botble\Table\Columns\{closure}(Object(Botble\Table\Columns\RowActionsColumn), '')
#37 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Supports\Renderable.php(59): call_user_func(Object(Closure), Object(Botble\Table\Columns\RowActionsColumn), '')
#38 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Columns\FormattedColumn.php(160): Botble\Table\Columns\FormattedColumn->rendering('')
#39 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(886): Botble\Table\Columns\FormattedColumn->renderCell(Object(Botble\Ecommerce\Models\BranchInventory), Object(Botble\Ecommerce\Tables\BranchInventoryTable))
#40 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Botble\Table\Abstracts\TableAbstract->Botble\Table\Abstracts\{closure}(Object(Botble\Ecommerce\Models\BranchInventory))
#41 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Util.php(43): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#42 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(84): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#43 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Object(Closure), Object(Closure))
#44 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Container.php(780): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Object(Closure), Array, NULL)
#45 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\Utilities\Helper.php(99): Illuminate\Container\Container->call(Object(Closure), Array)
#46 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\Processors\DataProcessor.php(107): Yajra\DataTables\Utilities\Helper::compileContent(Object(Closure), Array, Object(Botble\Ecommerce\Models\BranchInventory))
#47 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\Processors\DataProcessor.php(74): Yajra\DataTables\Processors\DataProcessor->addColumns(Array, Object(Botble\Ecommerce\Models\BranchInventory))
#48 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(835): Yajra\DataTables\Processors\DataProcessor->process(true)
#49 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(130): Yajra\DataTables\DataTableAbstract->processResults(Object(Illuminate\Database\Eloquent\Collection), true)
#50 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(920): Yajra\DataTables\QueryDataTable->make(true)
#51 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(335): Botble\Table\Abstracts\TableAbstract->toJson(Object(Botble\Table\EloquentDataTable))
#52 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Botble\Table\Abstracts\TableAbstract->ajax()
#53 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Util.php(43): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#54 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(84): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#55 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Object(Closure), Object(Closure))
#56 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Container.php(780): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Object(Closure), Array, NULL)
#57 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-buttons\src\Services\DataTable.php(187): Illuminate\Container\Container->call(Object(Closure))
#58 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(717): Yajra\DataTables\Services\DataTable->render('core/table::tab...', Array, Array)
#59 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(687): Botble\Table\Abstracts\TableAbstract->render('core/table::tab...', Array, Array)
#60 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Controllers\BranchInventoryController.php(30): Botble\Table\Abstracts\TableAbstract->renderTable(Array)
#61 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): Botble\Ecommerce\Http\Controllers\BranchInventoryController->index(Object(Botble\Ecommerce\Tables\BranchInventoryTable))
#62 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('index', Array)
#63 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Route.php(265): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(Botble\Ecommerce\Http\Controllers\BranchInventoryController), 'index')
#64 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Route.php(211): Illuminate\Routing\Route->runController()
#65 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(808): Illuminate\Routing\Route->run()
#66 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(169): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#67 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\CoreMiddleware.php(17): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#68 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(169): Botble\Base\Http\Middleware\CoreMiddleware->Botble\Base\Http\Middleware\{closure}(Object(Illuminate\Http\Request))
#69 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\EnsureLicenseHasBeenActivated.php(25): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#70 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\EnsureLicenseHasBeenActivated->handle(Object(Illuminate\Http\Request), Object(Closure))
#71 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(126): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#72 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\CoreMiddleware.php(16): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#73 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\CoreMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#74 D:\laragon\www\adawliahshop\platform\plugins\currency-symbol-updater\src\Http\Middleware\ReplaceSarCurrencySymbol.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#75 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\CurrencySymbolUpdater\Http\Middleware\ReplaceSarCurrencySymbol->handle(Object(Illuminate\Http\Request), Object(Closure))
#76 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Middleware\TrackAbandonedCart.php(18): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#77 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Ecommerce\Http\Middleware\TrackAbandonedCart->handle(Object(Illuminate\Http\Request), Object(Closure))
#78 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Middleware\CaptureCouponMiddleware.php(14): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#79 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Ecommerce\Http\Middleware\CaptureCouponMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#80 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Middleware\CaptureFootprintsMiddleware.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#81 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Ecommerce\Http\Middleware\CaptureFootprintsMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#82 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\HttpsProtocolMiddleware.php(16): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#83 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\HttpsProtocolMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#84 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\AdminLocaleMiddleware.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#85 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\AdminLocaleMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#86 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\LocaleMiddleware.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#87 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\LocaleMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#88 D:\laragon\www\adawliahshop\vendor\botble\installer\src\Http\Middleware\RedirectIfNotInstalledMiddleware.php(17): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#89 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Installer\Http\Middleware\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#90 D:\laragon\www\adawliahshop\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#91 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure))
#92 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#93 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#94 D:\laragon\www\adawliahshop\vendor\botble\platform\acl\src\Http\Middleware\Authenticate.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#95 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\ACL\Http\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#96 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(87): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#97 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#98 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(48): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#99 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#100 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(120): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#101 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(63): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#102 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#103 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#104 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#105 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(74): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#106 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#107 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(126): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#108 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(807): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#109 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(786): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#110 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(750): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#111 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(739): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#112 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#113 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(169): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#114 D:\laragon\www\adawliahshop\vendor\botble\platform\js-validation\src\RemoteValidationMiddleware.php(43): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#115 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\JsValidation\RemoteValidationMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#116 D:\laragon\www\adawliahshop\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#117 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure))
#118 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#119 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#120 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#121 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#122 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(51): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#123 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#124 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#125 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#126 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(109): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#127 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#128 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(48): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#129 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#130 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(58): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#131 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#132 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#133 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks->handle(Object(Illuminate\Http\Request), Object(Closure))
#134 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\ValidatePathEncoding.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#135 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\ValidatePathEncoding->handle(Object(Illuminate\Http\Request), Object(Closure))
#136 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(126): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#137 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#138 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#139 D:\laragon\www\adawliahshop\public\index.php(23): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#140 {main}

Next Illuminate\View\ViewException: Route [ecommerce.branch-inventory.edit] not defined. (View: D:\laragon\www\adawliahshop\platform\core\table\resources\views\actions\includes\action-attributes.blade.php) in D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\UrlGenerator.php:526
Stack trace:
#0 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(59): Illuminate\View\Engines\CompilerEngine->handleViewException(Object(Symfony\Component\Routing\Exception\RouteNotFoundException), 3)
#1 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(76): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\laragon\\www\\...', Array)
#2 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(208): Illuminate\View\Engines\CompilerEngine->get('D:\\laragon\\www\\...', Array)
#3 D:\laragon\www\adawliahshop\vendor\botble\shortcode\src\View\View.php(50): Illuminate\View\View->getContents()
#4 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(160): Botble\Shortcode\View\View->renderContents()
#5 D:\laragon\www\adawliahshop\storage\framework\views\db0fbf0420c2e35ad9892595e4fd2723.php(7): Illuminate\View\View->render()
#6 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Filesystem\Filesystem.php(123): require('D:\\laragon\\www\\...')
#7 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Filesystem\Filesystem.php(124): Illuminate\Filesystem\Filesystem::Illuminate\Filesystem\{closure}()
#8 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(57): Illuminate\Filesystem\Filesystem->getRequire('D:\\laragon\\www\\...', Array)
#9 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(76): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\laragon\\www\\...', Array)
#10 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(208): Illuminate\View\Engines\CompilerEngine->get('D:\\laragon\\www\\...', Array)
#11 D:\laragon\www\adawliahshop\vendor\botble\shortcode\src\View\View.php(50): Illuminate\View\View->getContents()
#12 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(160): Botble\Shortcode\View\View->renderContents()
#13 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableActionAbstract.php(112): Illuminate\View\View->render()
#14 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Collections\helpers.php(236): Botble\Table\Abstracts\TableActionAbstract->Botble\Table\Abstracts\{closure}()
#15 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Supports\Renderable.php(54): value(Object(Closure))
#16 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableActionAbstract.php(108): Botble\Table\Abstracts\TableActionAbstract->rendering(Object(Closure))
#17 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableActionAbstract.php(118): Botble\Table\Abstracts\TableActionAbstract->render()
#18 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Support\helpers.php(134): Botble\Table\Abstracts\TableActionAbstract->toHtml()
#19 D:\laragon\www\adawliahshop\storage\framework\views\4c96db657277edc9903923529034c412.php(10): e(Object(Botble\Table\Actions\EditAction))
#20 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Filesystem\Filesystem.php(123): require('D:\\laragon\\www\\...')
#21 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Filesystem\Filesystem.php(124): Illuminate\Filesystem\Filesystem::Illuminate\Filesystem\{closure}()
#22 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(57): Illuminate\Filesystem\Filesystem->getRequire('D:\\laragon\\www\\...', Array)
#23 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(76): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\laragon\\www\\...', Array)
#24 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(208): Illuminate\View\Engines\CompilerEngine->get('D:\\laragon\\www\\...', Array)
#25 D:\laragon\www\adawliahshop\vendor\botble\shortcode\src\View\View.php(50): Illuminate\View\View->getContents()
#26 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(160): Botble\Shortcode\View\View->renderContents()
#27 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Columns\RowActionsColumn.php(45): Illuminate\View\View->render()
#28 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Columns\FormattedColumn.php(44): Botble\Table\Columns\RowActionsColumn->formattedValue('')
#29 [internal function]: Botble\Table\Columns\FormattedColumn::Botble\Table\Columns\{closure}(Object(Botble\Table\Columns\RowActionsColumn), '')
#30 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Supports\Renderable.php(59): call_user_func(Object(Closure), Object(Botble\Table\Columns\RowActionsColumn), '')
#31 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Columns\FormattedColumn.php(160): Botble\Table\Columns\FormattedColumn->rendering('')
#32 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(886): Botble\Table\Columns\FormattedColumn->renderCell(Object(Botble\Ecommerce\Models\BranchInventory), Object(Botble\Ecommerce\Tables\BranchInventoryTable))
#33 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Botble\Table\Abstracts\TableAbstract->Botble\Table\Abstracts\{closure}(Object(Botble\Ecommerce\Models\BranchInventory))
#34 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Util.php(43): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#35 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(84): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#36 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Object(Closure), Object(Closure))
#37 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Container.php(780): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Object(Closure), Array, NULL)
#38 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\Utilities\Helper.php(99): Illuminate\Container\Container->call(Object(Closure), Array)
#39 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\Processors\DataProcessor.php(107): Yajra\DataTables\Utilities\Helper::compileContent(Object(Closure), Array, Object(Botble\Ecommerce\Models\BranchInventory))
#40 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\Processors\DataProcessor.php(74): Yajra\DataTables\Processors\DataProcessor->addColumns(Array, Object(Botble\Ecommerce\Models\BranchInventory))
#41 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(835): Yajra\DataTables\Processors\DataProcessor->process(true)
#42 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(130): Yajra\DataTables\DataTableAbstract->processResults(Object(Illuminate\Database\Eloquent\Collection), true)
#43 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(920): Yajra\DataTables\QueryDataTable->make(true)
#44 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(335): Botble\Table\Abstracts\TableAbstract->toJson(Object(Botble\Table\EloquentDataTable))
#45 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Botble\Table\Abstracts\TableAbstract->ajax()
#46 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Util.php(43): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#47 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(84): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#48 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Object(Closure), Object(Closure))
#49 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Container.php(780): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Object(Closure), Array, NULL)
#50 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-buttons\src\Services\DataTable.php(187): Illuminate\Container\Container->call(Object(Closure))
#51 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(717): Yajra\DataTables\Services\DataTable->render('core/table::tab...', Array, Array)
#52 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(687): Botble\Table\Abstracts\TableAbstract->render('core/table::tab...', Array, Array)
#53 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Controllers\BranchInventoryController.php(30): Botble\Table\Abstracts\TableAbstract->renderTable(Array)
#54 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): Botble\Ecommerce\Http\Controllers\BranchInventoryController->index(Object(Botble\Ecommerce\Tables\BranchInventoryTable))
#55 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('index', Array)
#56 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Route.php(265): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(Botble\Ecommerce\Http\Controllers\BranchInventoryController), 'index')
#57 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Route.php(211): Illuminate\Routing\Route->runController()
#58 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(808): Illuminate\Routing\Route->run()
#59 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(169): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#60 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\CoreMiddleware.php(17): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#61 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(169): Botble\Base\Http\Middleware\CoreMiddleware->Botble\Base\Http\Middleware\{closure}(Object(Illuminate\Http\Request))
#62 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\EnsureLicenseHasBeenActivated.php(25): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#63 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\EnsureLicenseHasBeenActivated->handle(Object(Illuminate\Http\Request), Object(Closure))
#64 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(126): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#65 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\CoreMiddleware.php(16): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#66 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\CoreMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#67 D:\laragon\www\adawliahshop\platform\plugins\currency-symbol-updater\src\Http\Middleware\ReplaceSarCurrencySymbol.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#68 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\CurrencySymbolUpdater\Http\Middleware\ReplaceSarCurrencySymbol->handle(Object(Illuminate\Http\Request), Object(Closure))
#69 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Middleware\TrackAbandonedCart.php(18): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#70 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Ecommerce\Http\Middleware\TrackAbandonedCart->handle(Object(Illuminate\Http\Request), Object(Closure))
#71 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Middleware\CaptureCouponMiddleware.php(14): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#72 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Ecommerce\Http\Middleware\CaptureCouponMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#73 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Middleware\CaptureFootprintsMiddleware.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#74 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Ecommerce\Http\Middleware\CaptureFootprintsMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#75 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\HttpsProtocolMiddleware.php(16): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#76 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\HttpsProtocolMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#77 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\AdminLocaleMiddleware.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#78 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\AdminLocaleMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#79 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\LocaleMiddleware.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#80 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\LocaleMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#81 D:\laragon\www\adawliahshop\vendor\botble\installer\src\Http\Middleware\RedirectIfNotInstalledMiddleware.php(17): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#82 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Installer\Http\Middleware\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#83 D:\laragon\www\adawliahshop\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#84 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure))
#85 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#86 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#87 D:\laragon\www\adawliahshop\vendor\botble\platform\acl\src\Http\Middleware\Authenticate.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#88 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\ACL\Http\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#89 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(87): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#90 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#91 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(48): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#92 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#93 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(120): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#94 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(63): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#95 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#96 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#97 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#98 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(74): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#99 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#100 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(126): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#101 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(807): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#102 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(786): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#103 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(750): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#104 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(739): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#105 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#106 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(169): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#107 D:\laragon\www\adawliahshop\vendor\botble\platform\js-validation\src\RemoteValidationMiddleware.php(43): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#108 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\JsValidation\RemoteValidationMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#109 D:\laragon\www\adawliahshop\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#110 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure))
#111 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#112 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#113 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#114 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#115 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(51): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#116 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#117 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#118 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#119 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(109): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#120 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#121 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(48): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#122 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#123 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(58): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#124 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#125 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#126 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks->handle(Object(Illuminate\Http\Request), Object(Closure))
#127 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\ValidatePathEncoding.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#128 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\ValidatePathEncoding->handle(Object(Illuminate\Http\Request), Object(Closure))
#129 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(126): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#130 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#131 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#132 D:\laragon\www\adawliahshop\public\index.php(23): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#133 {main}

Next Illuminate\View\ViewException: Route [ecommerce.branch-inventory.edit] not defined. (View: D:\laragon\www\adawliahshop\platform\core\table\resources\views\actions\includes\action-attributes.blade.php) (View: D:\laragon\www\adawliahshop\platform\core\table\resources\views\actions\includes\action-attributes.blade.php) in D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\UrlGenerator.php:526
Stack trace:
#0 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(59): Illuminate\View\Engines\CompilerEngine->handleViewException(Object(Illuminate\View\ViewException), 2)
#1 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(76): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\laragon\\www\\...', Array)
#2 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(208): Illuminate\View\Engines\CompilerEngine->get('D:\\laragon\\www\\...', Array)
#3 D:\laragon\www\adawliahshop\vendor\botble\shortcode\src\View\View.php(50): Illuminate\View\View->getContents()
#4 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(160): Botble\Shortcode\View\View->renderContents()
#5 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableActionAbstract.php(112): Illuminate\View\View->render()
#6 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Collections\helpers.php(236): Botble\Table\Abstracts\TableActionAbstract->Botble\Table\Abstracts\{closure}()
#7 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Supports\Renderable.php(54): value(Object(Closure))
#8 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableActionAbstract.php(108): Botble\Table\Abstracts\TableActionAbstract->rendering(Object(Closure))
#9 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableActionAbstract.php(118): Botble\Table\Abstracts\TableActionAbstract->render()
#10 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Support\helpers.php(134): Botble\Table\Abstracts\TableActionAbstract->toHtml()
#11 D:\laragon\www\adawliahshop\storage\framework\views\4c96db657277edc9903923529034c412.php(10): e(Object(Botble\Table\Actions\EditAction))
#12 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Filesystem\Filesystem.php(123): require('D:\\laragon\\www\\...')
#13 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Filesystem\Filesystem.php(124): Illuminate\Filesystem\Filesystem::Illuminate\Filesystem\{closure}()
#14 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(57): Illuminate\Filesystem\Filesystem->getRequire('D:\\laragon\\www\\...', Array)
#15 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(76): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\laragon\\www\\...', Array)
#16 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(208): Illuminate\View\Engines\CompilerEngine->get('D:\\laragon\\www\\...', Array)
#17 D:\laragon\www\adawliahshop\vendor\botble\shortcode\src\View\View.php(50): Illuminate\View\View->getContents()
#18 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(160): Botble\Shortcode\View\View->renderContents()
#19 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Columns\RowActionsColumn.php(45): Illuminate\View\View->render()
#20 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Columns\FormattedColumn.php(44): Botble\Table\Columns\RowActionsColumn->formattedValue('')
#21 [internal function]: Botble\Table\Columns\FormattedColumn::Botble\Table\Columns\{closure}(Object(Botble\Table\Columns\RowActionsColumn), '')
#22 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Supports\Renderable.php(59): call_user_func(Object(Closure), Object(Botble\Table\Columns\RowActionsColumn), '')
#23 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Columns\FormattedColumn.php(160): Botble\Table\Columns\FormattedColumn->rendering('')
#24 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(886): Botble\Table\Columns\FormattedColumn->renderCell(Object(Botble\Ecommerce\Models\BranchInventory), Object(Botble\Ecommerce\Tables\BranchInventoryTable))
#25 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Botble\Table\Abstracts\TableAbstract->Botble\Table\Abstracts\{closure}(Object(Botble\Ecommerce\Models\BranchInventory))
#26 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Util.php(43): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#27 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(84): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#28 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Object(Closure), Object(Closure))
#29 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Container.php(780): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Object(Closure), Array, NULL)
#30 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\Utilities\Helper.php(99): Illuminate\Container\Container->call(Object(Closure), Array)
#31 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\Processors\DataProcessor.php(107): Yajra\DataTables\Utilities\Helper::compileContent(Object(Closure), Array, Object(Botble\Ecommerce\Models\BranchInventory))
#32 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\Processors\DataProcessor.php(74): Yajra\DataTables\Processors\DataProcessor->addColumns(Array, Object(Botble\Ecommerce\Models\BranchInventory))
#33 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(835): Yajra\DataTables\Processors\DataProcessor->process(true)
#34 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(130): Yajra\DataTables\DataTableAbstract->processResults(Object(Illuminate\Database\Eloquent\Collection), true)
#35 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(920): Yajra\DataTables\QueryDataTable->make(true)
#36 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(335): Botble\Table\Abstracts\TableAbstract->toJson(Object(Botble\Table\EloquentDataTable))
#37 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Botble\Table\Abstracts\TableAbstract->ajax()
#38 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Util.php(43): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#39 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(84): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#40 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Object(Closure), Object(Closure))
#41 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Container.php(780): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Object(Closure), Array, NULL)
#42 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-buttons\src\Services\DataTable.php(187): Illuminate\Container\Container->call(Object(Closure))
#43 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(717): Yajra\DataTables\Services\DataTable->render('core/table::tab...', Array, Array)
#44 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(687): Botble\Table\Abstracts\TableAbstract->render('core/table::tab...', Array, Array)
#45 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Controllers\BranchInventoryController.php(30): Botble\Table\Abstracts\TableAbstract->renderTable(Array)
#46 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): Botble\Ecommerce\Http\Controllers\BranchInventoryController->index(Object(Botble\Ecommerce\Tables\BranchInventoryTable))
#47 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('index', Array)
#48 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Route.php(265): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(Botble\Ecommerce\Http\Controllers\BranchInventoryController), 'index')
#49 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Route.php(211): Illuminate\Routing\Route->runController()
#50 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(808): Illuminate\Routing\Route->run()
#51 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(169): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#52 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\CoreMiddleware.php(17): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(169): Botble\Base\Http\Middleware\CoreMiddleware->Botble\Base\Http\Middleware\{closure}(Object(Illuminate\Http\Request))
#54 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\EnsureLicenseHasBeenActivated.php(25): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#55 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\EnsureLicenseHasBeenActivated->handle(Object(Illuminate\Http\Request), Object(Closure))
#56 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(126): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#57 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\CoreMiddleware.php(16): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#58 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\CoreMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#59 D:\laragon\www\adawliahshop\platform\plugins\currency-symbol-updater\src\Http\Middleware\ReplaceSarCurrencySymbol.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#60 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\CurrencySymbolUpdater\Http\Middleware\ReplaceSarCurrencySymbol->handle(Object(Illuminate\Http\Request), Object(Closure))
#61 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Middleware\TrackAbandonedCart.php(18): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#62 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Ecommerce\Http\Middleware\TrackAbandonedCart->handle(Object(Illuminate\Http\Request), Object(Closure))
#63 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Middleware\CaptureCouponMiddleware.php(14): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#64 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Ecommerce\Http\Middleware\CaptureCouponMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#65 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Middleware\CaptureFootprintsMiddleware.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#66 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Ecommerce\Http\Middleware\CaptureFootprintsMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#67 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\HttpsProtocolMiddleware.php(16): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#68 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\HttpsProtocolMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#69 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\AdminLocaleMiddleware.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#70 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\AdminLocaleMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#71 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\LocaleMiddleware.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#72 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\LocaleMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#73 D:\laragon\www\adawliahshop\vendor\botble\installer\src\Http\Middleware\RedirectIfNotInstalledMiddleware.php(17): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#74 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Installer\Http\Middleware\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#75 D:\laragon\www\adawliahshop\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#76 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure))
#77 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#78 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#79 D:\laragon\www\adawliahshop\vendor\botble\platform\acl\src\Http\Middleware\Authenticate.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#80 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\ACL\Http\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#81 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(87): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#82 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#83 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(48): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#84 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#85 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(120): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#86 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(63): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#87 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#88 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#89 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#90 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(74): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#91 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#92 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(126): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#93 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(807): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#94 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(786): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#95 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(750): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#96 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(739): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#97 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#98 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(169): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#99 D:\laragon\www\adawliahshop\vendor\botble\platform\js-validation\src\RemoteValidationMiddleware.php(43): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#100 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\JsValidation\RemoteValidationMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#101 D:\laragon\www\adawliahshop\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#102 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure))
#103 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#104 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#105 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#106 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#107 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(51): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#108 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#109 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#110 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#111 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(109): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#112 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#113 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(48): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#114 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#115 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(58): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#116 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#117 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#118 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks->handle(Object(Illuminate\Http\Request), Object(Closure))
#119 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\ValidatePathEncoding.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#120 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\ValidatePathEncoding->handle(Object(Illuminate\Http\Request), Object(Closure))
#121 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(126): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#122 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#123 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#124 D:\laragon\www\adawliahshop\public\index.php(23): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#125 {main}

Next Illuminate\View\ViewException: Route [ecommerce.branch-inventory.edit] not defined. (View: D:\laragon\www\adawliahshop\platform\core\table\resources\views\actions\includes\action-attributes.blade.php) (View: D:\laragon\www\adawliahshop\platform\core\table\resources\views\actions\includes\action-attributes.blade.php) (View: D:\laragon\www\adawliahshop\platform\core\table\resources\views\actions\includes\action-attributes.blade.php) in D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\UrlGenerator.php:526
Stack trace:
#0 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\PhpEngine.php(59): Illuminate\View\Engines\CompilerEngine->handleViewException(Object(Illuminate\View\ViewException), 1)
#1 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Engines\CompilerEngine.php(76): Illuminate\View\Engines\PhpEngine->evaluatePath('D:\\laragon\\www\\...', Array)
#2 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(208): Illuminate\View\Engines\CompilerEngine->get('D:\\laragon\\www\\...', Array)
#3 D:\laragon\www\adawliahshop\vendor\botble\shortcode\src\View\View.php(50): Illuminate\View\View->getContents()
#4 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\View.php(160): Botble\Shortcode\View\View->renderContents()
#5 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Columns\RowActionsColumn.php(45): Illuminate\View\View->render()
#6 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Columns\FormattedColumn.php(44): Botble\Table\Columns\RowActionsColumn->formattedValue('')
#7 [internal function]: Botble\Table\Columns\FormattedColumn::Botble\Table\Columns\{closure}(Object(Botble\Table\Columns\RowActionsColumn), '')
#8 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Supports\Renderable.php(59): call_user_func(Object(Closure), Object(Botble\Table\Columns\RowActionsColumn), '')
#9 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Columns\FormattedColumn.php(160): Botble\Table\Columns\FormattedColumn->rendering('')
#10 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(886): Botble\Table\Columns\FormattedColumn->renderCell(Object(Botble\Ecommerce\Models\BranchInventory), Object(Botble\Ecommerce\Tables\BranchInventoryTable))
#11 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Botble\Table\Abstracts\TableAbstract->Botble\Table\Abstracts\{closure}(Object(Botble\Ecommerce\Models\BranchInventory))
#12 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Util.php(43): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#13 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(84): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#14 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Object(Closure), Object(Closure))
#15 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Container.php(780): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Object(Closure), Array, NULL)
#16 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\Utilities\Helper.php(99): Illuminate\Container\Container->call(Object(Closure), Array)
#17 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\Processors\DataProcessor.php(107): Yajra\DataTables\Utilities\Helper::compileContent(Object(Closure), Array, Object(Botble\Ecommerce\Models\BranchInventory))
#18 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\Processors\DataProcessor.php(74): Yajra\DataTables\Processors\DataProcessor->addColumns(Array, Object(Botble\Ecommerce\Models\BranchInventory))
#19 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(835): Yajra\DataTables\Processors\DataProcessor->process(true)
#20 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(130): Yajra\DataTables\DataTableAbstract->processResults(Object(Illuminate\Database\Eloquent\Collection), true)
#21 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(920): Yajra\DataTables\QueryDataTable->make(true)
#22 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(335): Botble\Table\Abstracts\TableAbstract->toJson(Object(Botble\Table\EloquentDataTable))
#23 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Botble\Table\Abstracts\TableAbstract->ajax()
#24 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Util.php(43): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#25 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(84): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#26 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Object(Closure), Object(Closure))
#27 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Container\Container.php(780): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Object(Closure), Array, NULL)
#28 D:\laragon\www\adawliahshop\vendor\yajra\laravel-datatables-buttons\src\Services\DataTable.php(187): Illuminate\Container\Container->call(Object(Closure))
#29 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(717): Yajra\DataTables\Services\DataTable->render('core/table::tab...', Array, Array)
#30 D:\laragon\www\adawliahshop\vendor\botble\platform\table\src\Abstracts\TableAbstract.php(687): Botble\Table\Abstracts\TableAbstract->render('core/table::tab...', Array, Array)
#31 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Controllers\BranchInventoryController.php(30): Botble\Table\Abstracts\TableAbstract->renderTable(Array)
#32 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): Botble\Ecommerce\Http\Controllers\BranchInventoryController->index(Object(Botble\Ecommerce\Tables\BranchInventoryTable))
#33 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction('index', Array)
#34 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Route.php(265): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(Botble\Ecommerce\Http\Controllers\BranchInventoryController), 'index')
#35 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Route.php(211): Illuminate\Routing\Route->runController()
#36 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(808): Illuminate\Routing\Route->run()
#37 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(169): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#38 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\CoreMiddleware.php(17): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(169): Botble\Base\Http\Middleware\CoreMiddleware->Botble\Base\Http\Middleware\{closure}(Object(Illuminate\Http\Request))
#40 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\EnsureLicenseHasBeenActivated.php(25): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\EnsureLicenseHasBeenActivated->handle(Object(Illuminate\Http\Request), Object(Closure))
#42 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(126): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#43 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\CoreMiddleware.php(16): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#44 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\CoreMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 D:\laragon\www\adawliahshop\platform\plugins\currency-symbol-updater\src\Http\Middleware\ReplaceSarCurrencySymbol.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\CurrencySymbolUpdater\Http\Middleware\ReplaceSarCurrencySymbol->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Middleware\TrackAbandonedCart.php(18): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Ecommerce\Http\Middleware\TrackAbandonedCart->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Middleware\CaptureCouponMiddleware.php(14): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Ecommerce\Http\Middleware\CaptureCouponMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 D:\laragon\www\adawliahshop\platform\plugins\ecommerce\src\Http\Middleware\CaptureFootprintsMiddleware.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Ecommerce\Http\Middleware\CaptureFootprintsMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\HttpsProtocolMiddleware.php(16): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\HttpsProtocolMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\AdminLocaleMiddleware.php(33): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\AdminLocaleMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 D:\laragon\www\adawliahshop\vendor\botble\platform\base\src\Http\Middleware\LocaleMiddleware.php(23): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Base\Http\Middleware\LocaleMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#59 D:\laragon\www\adawliahshop\vendor\botble\installer\src\Http\Middleware\RedirectIfNotInstalledMiddleware.php(17): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#60 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\Installer\Http\Middleware\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#61 D:\laragon\www\adawliahshop\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#62 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure))
#63 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#64 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#65 D:\laragon\www\adawliahshop\vendor\botble\platform\acl\src\Http\Middleware\Authenticate.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#66 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\ACL\Http\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#67 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(87): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#68 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#69 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(48): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#70 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#71 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(120): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#72 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(63): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#73 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#74 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#75 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#76 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(74): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#77 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#78 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(126): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#79 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(807): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#80 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(786): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#81 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(750): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#82 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Routing\Router.php(739): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#83 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#84 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(169): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#85 D:\laragon\www\adawliahshop\vendor\botble\platform\js-validation\src\RemoteValidationMiddleware.php(43): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#86 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Botble\JsValidation\RemoteValidationMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#87 D:\laragon\www\adawliahshop\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#88 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure))
#89 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#90 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#91 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#92 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#93 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(51): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#94 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#95 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#96 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#97 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(109): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#98 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#99 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(48): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#100 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#101 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(58): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#102 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#103 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#104 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks->handle(Object(Illuminate\Http\Request), Object(Closure))
#105 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Http\Middleware\ValidatePathEncoding.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#106 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(208): Illuminate\Http\Middleware\ValidatePathEncoding->handle(Object(Illuminate\Http\Request), Object(Closure))
#107 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(126): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#108 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#109 D:\laragon\www\adawliahshop\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#110 D:\laragon\www\adawliahshop\public\index.php(23): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#111 {main}  
